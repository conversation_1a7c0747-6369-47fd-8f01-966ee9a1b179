<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文章详情 - 广州立早网络科技有限公司</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 复用样式 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        /* 头部和导航 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px 0;
            color: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }
        
        .nav-links {
            display: flex;
            align-items: center;
            gap: 40px;
        }
        
        .nav-links a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 25px;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .nav-links a:hover,
        .nav-links a.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        /* 登录注册按钮样式 */
        .nav-auth {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .nav-auth a {
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .nav-auth .login-btn {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
        }
        
        .nav-auth .login-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }
        
        .nav-auth .register-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .nav-auth .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* 发布按钮样式 */
        .publish-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
            color: white !important;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .publish-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }
        
        .logout-btn {
            color: #2c3e50 !important;
            background: rgba(44, 62, 80, 0.1);
            border: 1px solid rgba(44, 62, 80, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }
        
        .logout-btn:hover {
            background: rgba(44, 62, 80, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(44, 62, 80, 0.2);
        }
        
        .user-btns {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-btns a {
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 主要内容区域 */
        .main-content {
            background: white;
            border-radius: 30px 30px 0 0;
            margin-top: 40px;
            position: relative;
            z-index: 10;
            box-shadow: 0 -10px 40px rgba(0,0,0,0.1);
            padding: 60px 0;
        }

        /* 面包屑导航 */
        .breadcrumb {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px 0;
            margin-bottom: 0;
        }

        .breadcrumb-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .breadcrumb-nav {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.95em;
        }

        .breadcrumb-nav a {
            color: #667eea;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb-nav a:hover {
            color: #764ba2;
        }

        .breadcrumb-nav span {
            color: #95a5a6;
        }

        .breadcrumb-nav .current {
            color: #2c3e50;
            font-weight: 500;
        }

        /* 文章详情样式 */
        .article-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .article-header {
            text-align: center;
            padding: 50px 0 40px;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 40px;
        }

        .article-category {
            display: inline-block;
            background: linear-gradient(135deg, #ff9500 0%, #ff6b00 100%);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(255, 149, 0, 0.3);
        }

        .article-title {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 700;
            line-height: 1.3;
        }

        .article-meta {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            color: #95a5a6;
            font-size: 0.95em;
        }

        .article-meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .article-meta i {
            color: #667eea;
        }

        .article-content {
            font-size: 1.1em;
            line-height: 1.8;
            color: #2c3e50;
            /* max-width: 800px; */
            margin: 0 auto;
        }

        .article-content h2 {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 40px 0 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
            font-weight: 600;
        }

        .article-content h3 {
            font-size: 1.4em;
            color: #2c3e50;
            margin: 30px 0 15px;
            font-weight: 600;
        }

        .article-content p {
            margin-bottom: 20px;
            text-align: justify;
        }

        .article-content ul,
        .article-content ol {
            margin: 20px 0;
            padding-left: 30px;
        }

        .article-content li {
            margin-bottom: 10px;
        }

        .article-content blockquote {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 4px solid #667eea;
            padding: 20px 25px;
            margin: 30px 0;
            border-radius: 0 10px 10px 0;
            font-style: italic;
            color: #5a6c7d;
        }

        .article-content code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e83e8c;
        }

        .article-content pre {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            overflow-x: auto;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }

        /* 返回按钮 */
        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            margin-top: 40px;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* 页脚样式 */
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px 0 20px;
            margin-top: 0;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            /*margin-bottom: 50px;*/
            position: relative;
            z-index: 1;
        }

        .footer-section h4 {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: white;
            position: relative;
            padding-bottom: 8px;
            font-weight: 600;
        }

        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 30px;
            height: 2px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 8px;
        }

        .footer-section ul li a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.95em;
            display: inline-block;
        }

        .footer-section ul li a:hover {
            color: #667eea;
            transform: translateX(5px);
        }

        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .contact-info:hover {
            color: white;
            transform: translateX(5px);
        }

        .contact-info i {
            margin-right: 10px;
            color: #667eea;
            font-size: 1em;
            width: 16px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: rgba(255,255,255,0.6);
            position: relative;
            z-index: 1;
        }

        .footer-bottom p {
            margin-bottom: 5px;
            font-size: 0.85em;
            line-height: 1.4;
        }

        .footer-bottom a {
            color: rgba(255,255,255,0.8);
            transition: color 0.3s ease;
        }

        .footer-bottom a:hover {
            color: #667eea;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 20px;
            }

            .article-container {
                padding: 0 15px;
            }

            .article-title {
                font-size: 2em;
            }

            .article-meta {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                gap: 20px;
            }

            .nav-auth {
                gap: 15px;
            }

            .nav-auth a,
            .user-btns a {
                padding: 10px 20px;
                font-size: 0.9em;
            }
        }

        @media (max-width: 480px) {
            .article-title {
                font-size: 1.6em;
            }

            .nav-container {
                flex-direction: column;
                height: auto;
                gap: 20px;
            }

            .article-header {
                padding: 30px 0 25px;
            }

            .breadcrumb-nav {
                font-size: 0.85em;
            }
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><img src="img/logo.png" height="50" /></h1>
        </div>
    </div>
    
    <div class="nav">
        <div class="container nav-container">
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns" style="display:none">
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html">游戏中心</a>
                <!-- <a href="products-wx.html">小程序开发</a>
                <a href="products-soft.html">管理软件开发</a> -->
                <a href="articles.html" class="active">防沉迷公告</a>
                <a href="about.html">关于我们</a>
                <!-- <a href="contact.html">联系我们</a> -->
            </div>
        </div>
    </div>


    <div class="main-content">
        <div class="article-container">
            <div class="article-header">
                <div class="article-category" id="articleCategory">防沉迷公告</div>
                <h1 class="article-title" id="articleTitle">文章标题</h1>
                <div class="article-meta">
                    <div class="article-meta-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span id="articleDate">2025-07-11</span>
                    </div>
                    <!-- <div class="article-meta-item">
                        <i class="fas fa-eye"></i>
                        <span id="articleViews">128</span> 次阅读
                    </div> -->
                    <div class="article-meta-item">
                        <i class="fas fa-user"></i>
                        <span>立早网络</span>
                    </div>
                </div>
            </div>

            <div class="article-content" id="articleContent">
                <!-- 文章内容将通过JavaScript动态加载 -->
            </div>

            <a href="articles.html" class="back-button">
                <i class="fas fa-arrow-left"></i>
                返回文章列表
            </a>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>

    <script>
        // 文章数据
        const articles = {
            'jiachang-jianhu': {
                category: '防沉迷公告',
                title: '家长监护工程介绍',
                date: '2025-07-11',
                views: 321,
                content: `

            <h3 style="margin: 0px; padding: 0px; font-size: 16px; font-weight: 500; font-family: Verdana, Helvetica; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(50, 50, 50); -webkit-font-smoothing: antialiased; -webkit-tap-highlight-color: transparent; word-break: break-all; line-height: 30px;"><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153);">“网络游戏未成年人家长监护工程”是一项由中华人民共和国文化部指导，多家网络游戏企业共同发起并参与实施，旨在加强家长对未成年人参与网络游戏的监护，引导未成年人健康、绿色参与网络游戏，和谐家庭关系的社会性公益行动。它提供了一种切实可行的方法，一种家长实施监控的管道，使家长纠正部分未成年子女沉迷游戏的行为成为可能。该项社会公益行动充分反映了中国网络游戏行业高度的社会责任感，对未成年玩家合法权益的关注及对用实际行动营造和谐社会的愿望。</span></h3><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255);"><br></p><p class="news_text" style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><br></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255);"><span style="color: rgb(0, 0, 0);"><strong><span style="margin: 0px; padding: 0px; box-sizing: border-box; font-size: 16px;">未成年人健康参与网络游戏提示</span></strong></span></p><p class="news_text" style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">随着网络在青少年中的普及，未成年人接触网络游戏已经成为普遍现象。为保护未成年人健康参与游戏，在政府进一步加强行业管理的前提下，家长也应当加强监护引导。为此，我们为未成年人参与网络游戏提供以下意见：<br>1.主动控制游戏时间。游戏只是学习、生活的调剂，要积极参与线下的各类活动，并让父母了解自己在网络游戏中的行为和体验。<br>2.不参与可能耗费较多时间的游戏设置。不玩大型角色扮演类游戏，不玩有PK类设置的游戏。在校学生每周玩游戏不超过2小时，每月在游戏中的花费不超过10元。<br>3.不要将游戏当作精神寄托。尤其在现实生活中遇到压力和挫折时，应多与家人朋友交流倾诉，不要只依靠游戏来缓解压力。<br>4.养成积极健康的游戏心态。克服攀比、炫耀、仇恨和报复等心理，避免形成欺凌弱小、抢劫他人等不良网络行为习惯。<br>5.注意保护个人信息。包括个人家庭、朋友身份信息，家庭、学校、单位地址，电话号码等，防范网络陷阱和网络犯罪。</span></p><p class="news_text" style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><br></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255);"><strong><span style="margin: 0px; padding: 0px; box-sizing: border-box; font-size: 16px; color: rgb(0, 0, 0);">家长监护申请流程监护申请流程说明</span></strong></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><br></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255);"><strong><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">申请条件：</span></strong><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;"><br></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">1、 申请人需为被监护未成年人的法定监护人；</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">2、 申请人的被监护人年龄小于18周岁；<br></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">3、 申请人需为大陆公民，不含港、澳、台人士。</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><br></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><strong><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">申请方式：</span></strong></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255);"><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">1.电话服务 监护人可以通过来专线电话来咨询，7*24小时，全年无休，邮箱：<EMAIL><br>2.传真服务 监护人可以通过传真发送相关信息，进行申请，传真24小时受理，邮箱：<EMAIL><br></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">3.邮寄服务 监护人可以通过邮寄提交相关信息，邮寄地址：广东省广州市天河区天河路490号3907单元</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><br></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><strong><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">其他要求：</span></strong></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255);"><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">1.申请人应提交较完备的申请材料，对未提供的信息要及时补充；可请熟知电脑、互联网、游戏的人员进行协助，以便提供详实资料；<br>2.申请人应保证提交的信息真实有效；对于提供虚假信息或伪造证件，我司将保留进一步追究法律责任的权利。<br><br><strong>监督方式：</strong><br>1.封停账号<br></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">2.根据监护人意愿封停被监护人账号。直至被监护人年满18周岁，并提出申请。</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><br></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255);"><strong><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">监护进度查询:</span></strong><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;"><br></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">如果您已经申请家长监护服务，您可以通过我们的在线电话进行查询，了解您所提交的服务申请最新处理进展，如：资料是否收到，是否需要后续提交信息，账号是否已经进行处理等。</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); box-sizing: border-box; color: rgb(89, 90, 92);"><span style="margin: 0px; padding: 0px; box-sizing: border-box; color: rgb(153, 153, 153); font-size: 16px;">服务期间，如果您对需要提交的信息或者处理结果有疑问，或者其他任何问题，您均可以随时联系我们，我们将由专门负责的客服主管为您提供咨询解答服务，或者配合、指导您解决问题。</span></p><p><br></p><p><br></p>
`
            },
            'qingshaonian-fangchenmi': {
                category: '防沉迷公告',
                title: '青少年防沉迷',
                date: '2025-07-11',
                views: 195,
                content: `
            <h3 style="margin: 0px; padding: 0px; font-size: 16px; font-weight: 500; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; text-wrap: wrap; background-color: rgb(255, 255, 255);">什么是防沉迷系统？</h3><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">防沉迷系统，是根据政府《网络游戏防沉迷系统开发标准》及其相关要求开发实施，自2007年7月16日起已正式实施。旨在解决未成年人沉迷网络游戏的现状，让未成年人无法依赖长时间的在线来获得游戏内个人能力的增长，报偿值的增加，有效控制未成年人使用者的在线时间，改变不利于未成年人身心健康的不良游戏习惯。</p><h3 style="margin: 0px; padding: 0px; font-size: 16px; font-weight: 500; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; text-wrap: wrap; background-color: rgb(255, 255, 255);">防沉迷系统设计目的？</h3><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">防止未成年人过度游戏，倡导健康游戏习惯，保护未成年人的合法权益；帮助法定监护人了解其监护对象是否参与此网络游戏、是否受到防沉迷系统的保护等情况；在实现上述目的的同时，兼顾成年玩家自主支配其游戏时间的合法权益。</p><h3 style="margin: 0px; padding: 0px; font-size: 16px; font-weight: 500; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; text-wrap: wrap; background-color: rgb(255, 255, 255);">实名认证系统与防沉迷系统的关系是怎样？</h3><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">实名认证系统用于收集玩家的身份证号及姓名，并以此作为判断玩家是否需要受到防沉 迷系统保护的重要依据。<br>实名信息显示为不满18周岁的用户，将被初步判定为未成年人，纳入防沉迷状态。其实名信息于成年后用户自行至实名认证提交至公安机关进行验证。<br>实名信息显示为已满18周岁的用户，将被初步判定为成年人。其实名信息在等待公安部门验证前将被处于非防沉迷状态，如果通过公安部门的实名验证，则正式进入非防沉迷状态。如失败，则被纳入防沉迷状态。在注册后任何时间用户都可自行至实名认证提交实名信息至公安机关进行验证。<br>实名信息验证成功的帐号不纳入防沉迷系统。相反，认证失败的帐号将被纳入防沉迷系统。</p><h3 style="margin: 0px; padding: 0px; font-size: 16px; font-weight: 500; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; text-wrap: wrap; background-color: rgb(255, 255, 255);">防沉迷系统的具体内容和执行方法？</h3><h3 style="margin: 0px; padding: 0px; font-size: 16px; font-weight: 500; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; text-wrap: wrap; background-color: rgb(255, 255, 255);">确定健康游戏时间标准</h3><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">1. 定义使用者累计3小时以内的游戏时间为“健康”游戏时间。<br>2. 定义使用者在累计游戏3小时之后，再持续下去的2小时游戏时间为“疲劳”游戏时间。<br>3. 定义使用者累计游戏时间超过5小时为“不健康”游戏时间。</p><h3 style="margin: 0px; padding: 0px; font-size: 16px; font-weight: 500; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; text-wrap: wrap; background-color: rgb(255, 255, 255);">促进使用者养成健康的游戏习惯</h3><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">为保障使用者适度使用并有足够的休息时间，对游戏的间隔时间和收益进行限制和引导的处理办法：</p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">1. 根据以上考虑，不同累计在线时间的游戏收益处理如下：累计在线时间在3小时以内，游戏收益为正常；3-5小时内，收益降为正常值的50%；5小时以上，收益降为0。</p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">2. 由于不同的游戏有不同范畴，因此对于当前角色扮演类的网络游戏，特别是目前将作为试点的游戏，建议定义为“游戏收益=游戏中获得的经验值＋获得的虚拟物品”。收益为50％，则指获得经验值减半，虚拟物品减半。收益为0,则指无法获得经验值，无法获得虚拟物品。</p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">3. 定义使用者在累计游戏3小时之后，再持续下去的2小时游戏时间为“疲劳”游戏时间。</p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">4. 定义使用者累计游戏时间超过5小时为“不健康”游戏时间。</p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">初始化累计时间——由于使用者上下线的行为比较复杂，会出现以下多种情况，因此限时与提示的实现方法如下：</p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">使用者在线后，其持续在线时间将累计计算，称为“累计在线时间”。</p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">使用者下线后，其不在线时间也将累计计算，称为“累计下线时间”。</p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">使用者累计在线时间在3小时以内的，游戏收益正常。每累计在线时间满1小时，应提醒一次：“您累计在线时间已满1小时。”至累计在线时间满3小时时，应提醒：“您累计在线时间已满3小时，请您下线休息，做适当身体活动。</p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">”如果累计在线时间超过3小时进入第4－5小时，在开始进入时就应做出警示：“您已经进入疲劳游戏时间，您的游戏收益将降为正常值的50％，请您尽快下线休息，做适当身体活动。”此后，应每30分钟警示一次。</p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">如果累计在线时间超过5小时进入第6小时，在开始进入时就应做出警示：“您已进入不健康游戏时间，请您立即下线休息。如不下线，您的身体健康将受到损害，您的收益已降为零。”此后，应每15分钟警示一次。</p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">受防沉迷系统限制的用户，当下线时间超过5小时时，累计游戏时间初始化为0。初始化后进入游戏就会开始重新计算累计游戏时间。</p><h3 style="margin: 0px; padding: 0px; font-size: 16px; font-weight: 500; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; text-wrap: wrap; background-color: rgb(255, 255, 255);">防沉迷系统介绍</h3><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">据政府相关规定，2007年7月15日防沉迷系统正式推出后，未满18周岁的玩家将被纳入防沉迷范围。届时被纳入防沉迷的玩家，游戏收益将会受损。特此，提醒各位玩家，根据以下流程，尽快提交您的年龄段、姓名和身份证信息，只需半分钟！</p><h3 style="margin: 0px; padding: 0px; font-size: 16px; font-weight: 500; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; text-wrap: wrap; background-color: rgb(255, 255, 255);">第一步 填写注册资料</h3><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">进入账号注册页面后，请根据提示输入相关内容。创建密码时，尽量用多种字符组合，这样可以提高帐号的安全性。</p><h3 style="margin: 0px; padding: 0px; font-size: 16px; font-weight: 500; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; text-wrap: wrap; background-color: rgb(255, 255, 255);">第二步 防沉迷认证</h3><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">注册成功后，请填写身份证和真实姓名进行防沉迷认证。 注意：如果您是未成年人，将被纳入防沉迷系统中，受到防沉迷系统的限制。</p><h3 style="margin: 0px; padding: 0px; font-size: 16px; font-weight: 500; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; text-wrap: wrap; background-color: rgb(255, 255, 255);">关于防沉迷系统</h3><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(60, 60, 60); font-family: Verdana, Helvetica; font-size: 16px; text-wrap: wrap; background-color: rgb(255, 255, 255); line-height: 30px;">根据政府相关规定，2007年7月15日防沉迷系统正式推出后,未满18周岁的玩家将被纳入防沉迷范围。 2008年1月15日后对于注册时身份信息不全、身份证号与姓名不一致的玩家也将被纳入防沉迷范围。届时被纳入防沉迷的玩家，游戏收益将会受损。特此，提 醒各位玩家，根据以上流程，尽快提交您的年龄段、姓名和身份证信息， 只需半分钟！</p><p><br></p>
                `
            },
        };

        // 检查登录状态
        function checkLoginStatus() {
            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
            const authBtns = document.getElementById('authBtns');
            const userBtns = document.getElementById('userBtns');

            if (isLoggedIn) {
                authBtns.style.display = 'none';
                userBtns.style.display = 'flex';
            } else {
                authBtns.style.display = 'flex';
                userBtns.style.display = 'none';
            }
        }

        // 退出登录
        function handleLogout() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('userInfo');
            checkLoginStatus();
            alert('已成功退出登录');
        }

        // 加载文章内容
        function loadArticle() {
            const urlParams = new URLSearchParams(window.location.search);
            const articleId = urlParams.get('id');

            if (articleId && articles[articleId]) {
                const article = articles[articleId];

                // 更新页面内容
                document.getElementById('articleCategory').textContent = article.category;
                document.getElementById('articleTitle').textContent = article.title;
                document.getElementById('articleDate').textContent = article.date;
                // document.getElementById('articleViews').textContent = article.views;
                document.getElementById('articleContent').innerHTML = article.content;
                document.getElementById('breadcrumbTitle').textContent = article.title;

                // 更新页面标题
                document.title = article.title + ' - 广州立早网络科技有限公司';

                // 增加阅读量
                article.views++;
                document.getElementById('articleViews').textContent = article.views;
            } else {
                // 文章不存在，显示默认内容
                document.getElementById('articleContent').innerHTML = `
                    <div style="text-align: center; padding: 50px 0;">
                        <h2>文章不存在</h2>
                        <p>抱歉，您访问的文章不存在或已被删除。</p>
                        <a href="articles.html" class="back-button" style="margin-top: 20px;">
                            <i class="fas fa-arrow-left"></i>
                            返回文章列表
                        </a>
                    </div>
                `;
            }
        }

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            loadArticle();
        });
    </script>
</body>
</html>
