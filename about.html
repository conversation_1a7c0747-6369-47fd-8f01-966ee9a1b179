<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于我们</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 复用样式 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        /* 头部和导航 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px 0;
            color: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .nav-links a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 25px;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        /* 登录注册按钮样式 */
        .nav-auth {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-auth a {
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-auth .login-btn {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .nav-auth .login-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }

        .nav-auth .register-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-auth .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        /* 主要内容区域 */
        .main-content {
            background: white;
            border-radius: 30px 30px 0 0;
            margin-top: 40px;
            position: relative;
            z-index: 10;
            box-shadow: 0 -10px 40px rgba(0,0,0,0.1);
            /* padding: 60px 0; */
        }

        /* 页面标题区域 */
        .page-header {
            text-align: center;
            /* padding: 60px 0; */
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            margin-bottom: 0;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .page-header h1 {
            font-size: 3.2em;
            color: white;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .page-header p {
            font-size: 1.3em;
            color: rgba(255,255,255,0.9);
            max-width: 600px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        /* 页脚样式 */
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px 0 20px;
            margin-top: 0;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            /*margin-bottom: 50px;*/
            position: relative;
            z-index: 1;
        }

        .footer-section h4 {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: white;
            position: relative;
            padding-bottom: 8px;
            font-weight: 600;
        }

        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 30px;
            height: 2px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 8px;
        }

        .footer-section ul li a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.95em;
            display: inline-block;
        }

        .footer-section ul li a:hover {
            color: #667eea;
            transform: translateX(5px);
        }

        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .contact-info:hover {
            color: white;
            transform: translateX(5px);
        }

        .contact-info i {
            margin-right: 10px;
            color: #667eea;
            font-size: 1em;
            width: 16px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: rgba(255,255,255,0.6);
            position: relative;
            z-index: 1;
        }

        .footer-bottom p {
            margin-bottom: 5px;
            font-size: 0.85em;
            line-height: 1.4;
        }

        .footer-bottom a {
            color: rgba(255,255,255,0.8);
            transition: color 0.3s ease;
        }

        .footer-bottom a:hover {
            color: #667eea;
        }
        
        /* 关于我们特定样式 */
        .about-section {
            margin-bottom: 80px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 50px 40px;
            border-radius: 25px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
        }

        .about-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0.02;
            transition: opacity 0.4s ease;
        }

        .about-section:hover::before {
            opacity: 0.05;
        }

        .about-section h3 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 35px;
            position: relative;
            padding-left: 25px;
            font-weight: 700;
            z-index: 1;
        }

        .about-section h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 6px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 3px;
            box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
        }

        .about-section h3::after {
            content: '';
            position: absolute;
            left: 25px;
            bottom: -5px;
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #ff9500 0%, #ff6b00 100%);
            border-radius: 2px;
        }

        .about-section p {
            color: #2c3e50;
            font-size: 1.2em;
            line-height: 2;
            margin-bottom: 25px;
            text-align: justify;
            text-indent: 2em;
            position: relative;
            z-index: 1;
            font-weight: 400;
        }

        .about-section p:first-of-type {
            font-size: 1.25em;
            color: #34495e;
            font-weight: 500;
        }

        .about-section p:last-of-type {
            margin-bottom: 0;
        }

        /* 添加段落间的分隔效果 */
        .about-section p + p {
            position: relative;
            padding-top: 20px;
        }

        .about-section p + p::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 2px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1px;
            opacity: 0.3;
        }

        /* 数据统计样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin: 60px 0;
            text-align: center;
        }

        .stat-item {
            padding: 40px 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 25px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .stat-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 60px rgba(102, 126, 234, 0.2);
        }

        .stat-item:hover::before {
            opacity: 0.05;
        }

        .stat-item .number {
            font-size: 3.2em;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .stat-item .label {
            color: #5a6c7d;
            font-size: 1.1em;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }

        /* 发展历程样式 */
        .timeline {
            margin: 40px 0;
            position: relative;
            padding-left: 50px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #2196F3;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            padding-left: 30px;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -10px;
            top: 5px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #2196F3;
            border: 4px solid #fff;
            box-shadow: 0 0 0 2px #2196F3;
        }
        
        .timeline-item .year {
            font-size: 1.2em;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 5px;
        }
        
        .timeline-item .content h4 {
            color: #333;
            margin-bottom: 5px;
        }
        
        .timeline-item .content p {
            color: #666;
        }

        /* 技术优势样式 */
        .tech-advantages {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 40px;
            margin-top: 50px;
        }

        .tech-item {
            text-align: center;
            padding: 45px 35px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 25px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        }

        .tech-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .tech-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 60px rgba(102, 126, 234, 0.2);
        }

        .tech-item:hover::before {
            opacity: 0.05;
        }

        .tech-item i {
            font-size: 3.5em;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
        }

        .tech-item h4 {
            margin-bottom: 18px;
            color: #2c3e50;
            font-size: 1.4em;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .tech-item p {
            color: #5a6c7d;
            line-height: 1.6;
            font-size: 1.05em;
            position: relative;
            z-index: 1;
        }

        /* 服务承诺样式 */
        .service-promises {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 40px;
            margin-top: 50px;
        }

        .promise-item {
            text-align: center;
            padding: 45px 35px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 25px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        }

        .promise-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .promise-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 60px rgba(102, 126, 234, 0.2);
        }

        .promise-item:hover::before {
            opacity: 0.05;
        }

        .promise-item i {
            font-size: 3.5em;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
        }

        .promise-item h4 {
            margin-bottom: 18px;
            color: #2c3e50;
            font-size: 1.4em;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .promise-item p {
            color: #5a6c7d;
            line-height: 1.6;
            font-size: 1.05em;
            position: relative;
            z-index: 1;
        }
        /* 发布按钮样式 */
        .publish-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
            color: white !important;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .publish-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .logout-btn {
            color: #2c3e50 !important;
            background: rgba(44, 62, 80, 0.1);
            border: 1px solid rgba(44, 62, 80, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .logout-btn:hover {
            background: rgba(44, 62, 80, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(44, 62, 80, 0.2);
        }

        .user-btns {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-btns a {
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 20px;
            }

            .page-header h1 {
                font-size: 2.5em;
            }

            .stats-grid,
            .tech-advantages,
            .service-promises {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .nav-links {
                gap: 20px;
            }

            .nav-auth {
                gap: 15px;
            }

            .nav-auth a,
            .user-btns a {
                padding: 10px 20px;
                font-size: 0.9em;
            }
        }

        @media (max-width: 480px) {
            .page-header h1 {
                font-size: 2em;
            }

            .nav-container {
                flex-direction: column;
                height: auto;
                gap: 20px;
            }

            .about-section {
                padding: 35px 25px;
                margin-bottom: 50px;
            }

            .about-section h3 {
                font-size: 2em;
                padding-left: 20px;
            }

            .about-section p {
                font-size: 1.1em;
                line-height: 1.8;
                text-indent: 1.5em;
            }
        }

        @media (max-width: 480px) {
            .about-section {
                padding: 25px 20px;
                margin-bottom: 40px;
            }

            .about-section h3 {
                font-size: 1.8em;
                padding-left: 18px;
            }

            .about-section p {
                font-size: 1.05em;
                text-indent: 1em;
            }
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><img src="img/logo.png" height="50" /></h1>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav">
        <div class="container nav-container">
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html">游戏中心</a>
                <!-- <a href="products-wx.html">小程序开发</a>
                <a href="products-soft.html">管理软件开发</a> -->
                <a href="articles.html">防沉迷公告</a>
                <a href="about.html" class="active">关于我们</a>
                <!-- <a href="contact.html">联系我们</a> -->
            </div>
        </div>
    </div>

    <div class="page-header">
        <!-- <div class="container">
            <h1>关于我们</h1>
        </div> -->
    </div>

    <div class="main-content">
        <div class="container">
            <!-- 公司简介 -->
            <div class="about-section">
                <h3>公司简介</h3>
                <p> 广州立早网络科技有限公司成立于2016年，坐落于中国广州，是一家集自主研发、独代发行和产品运营等业务为一体的互联网游戏公司。自成立以来，立早网络保持着高速发展的势头，员工人数由最初的十余人发展至现在的近百人，其中90%为专业的游戏技术和运营人员，均来自国内各大游戏及相关互联网公司，拥有着丰富的研发、运营、推广和管理的经验，现已成功自研自发多款单机游戏和网络游戏，并获得数百款网络游戏的发行运营权，为公司产品的制作开发及运营发行提供了强有力的保证。用创新的精神研发和运营游戏是立早网络发展的重要目标，致力于为用户提供优质内容服务和良好的娱乐体验是立早网络发展的清晰使命。未来，立早网络将进一步提升核心竞争力，持续打造优质且经典的游戏作品，致力于将中国的游戏运作为世界的游戏。</p>
            </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>
    <script src="js/login.js"></script>
</body>
</html> 