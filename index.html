<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 复用样式 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        /* 头部和导航 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px 0;
            color: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .nav-links a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 25px;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        /* 英雄区域 */
        .hero {
            text-align: center;
            padding: 120px 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            margin-bottom: 80px;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero h2 {
            font-size: 3.5em;
            color: white;
            margin-bottom: 25px;
            font-weight: 700;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .hero p {
            font-size: 1.4em;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            z-index: 1;
        }

        .hero-buttons {
            display: flex;
            justify-content: center;
            gap: 25px;
            position: relative;
            z-index: 1;
        }

        .hero-button {
            display: inline-block;
            padding: 18px 45px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1em;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .primary-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            box-shadow: 0 8px 30px rgba(255, 107, 107, 0.4);
        }

        .primary-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
        }

        .secondary-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .secondary-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 255, 255, 0.2);
        }

        /* 主要内容区域 */
        .main-content {
            background: white;
            border-radius: 30px 30px 0 0;
            margin-top: -50px;
            position: relative;
            z-index: 10;
            box-shadow: 0 -10px 40px rgba(0,0,0,0.1);
        }

        /* 特点展示 */
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            padding: 80px 0 60px;
        }

        .feature-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 50px 40px;
            border-radius: 25px;
            text-align: center;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
        }

        .feature-card:hover::before {
            opacity: 0.05;
        }

        .feature-icon {
            font-size: 3.5em;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
        }

        .feature-card h3 {
            margin-bottom: 20px;
            font-size: 1.8em;
            color: #2c3e50;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .feature-card p {
            color: #5a6c7d;
            font-size: 1.1em;
            line-height: 1.7;
            position: relative;
            z-index: 1;
        }

        /* 优势展示 */
        .advantages {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 100px 0;
            margin: 80px 0;
            position: relative;
        }

        .advantages::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(102,126,234,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
        }

        .advantages h2 {
            text-align: center;
            font-size: 3.2em;
            margin-bottom: 60px;
            color: #2c3e50;
            font-weight: 700;
            position: relative;
            z-index: 1;
        }

        .advantages h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            position: relative;
            z-index: 1;
        }

        .advantage-card {
            background: white;
            padding: 45px 35px;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.08);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
        }

        .advantage-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .advantage-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 60px rgba(102, 126, 234, 0.2);
        }

        .advantage-icon {
            font-size: 3.2em;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 25px;
        }

        .advantage-card h3 {
            font-size: 1.6em;
            color: #2c3e50;
            margin-bottom: 18px;
            font-weight: 600;
        }

        .advantage-card p {
            color: #5a6c7d;
            line-height: 1.7;
            font-size: 1.05em;
        }

        /* 数据统计 */
        .stats {
            padding: 80px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .stats::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagon" width="50" height="50" patternUnits="userSpaceOnUse"><polygon points="25,5 45,15 45,35 25,45 5,35 5,15" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagon)"/></svg>');
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 50px;
            position: relative;
            z-index: 1;
        }

        .stat-item {
            padding: 30px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .stat-number {
            font-size: 3.5em;
            font-weight: 700;
            color: white;
            margin-bottom: 15px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.2em;
            font-weight: 500;
        }

        /* 页脚样式 */
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px 0 20px;
            margin-top: 0;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            /*margin-bottom: 25px;*/
            position: relative;
            z-index: 1;
        }

        .footer-section h4 {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: white;
            position: relative;
            padding-bottom: 8px;
            font-weight: 600;
        }

        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 30px;
            height: 2px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 8px;
        }

        .footer-section ul li a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.95em;
            display: inline-block;
        }

        .footer-section ul li a:hover {
            color: #667eea;
            transform: translateX(5px);
        }

        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .contact-info:hover {
            color: white;
            transform: translateX(5px);
        }

        .contact-info i {
            margin-right: 10px;
            color: #667eea;
            font-size: 1em;
            width: 16px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: rgba(255,255,255,0.6);
            position: relative;
            z-index: 1;
        }

        .footer-bottom p {
            margin-bottom: 5px;
            font-size: 0.85em;
            line-height: 1.4;
        }

        .footer-bottom a {
            color: rgba(255,255,255,0.8);
            transition: color 0.3s ease;
        }

        .footer-bottom a:hover {
            color: #667eea;
        }

        /* 登录注册按钮样式 */
        .nav-auth {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-auth a {
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-auth .login-btn {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .nav-auth .login-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }

        .nav-auth .register-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-auth .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* 修改发布按钮样式 */
        .publish-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
            color: white !important;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .publish-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .logout-btn {
            color: #2c3e50 !important;
            background: rgba(44, 62, 80, 0.1);
            border: 1px solid rgba(44, 62, 80, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .logout-btn:hover {
            background: rgba(44, 62, 80, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(44, 62, 80, 0.2);
        }

        .user-btns {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-btns a {
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 20px;
            }

            .hero h2 {
                font-size: 2.5em;
            }

            .hero p {
                font-size: 1.2em;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .features {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .advantages-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 30px;
            }

            .nav-links {
                gap: 20px;
            }

            .nav-auth {
                gap: 15px;
            }

            .nav-auth a,
            .user-btns a {
                padding: 10px 20px;
                font-size: 0.9em;
            }
        }

        @media (max-width: 480px) {
            .hero h2 {
                font-size: 2em;
            }

            .advantages h2 {
                font-size: 2.5em;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .nav-container {
                flex-direction: column;
                height: auto;
                gap: 20px;
            }
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><img src="img/logo.png" height="50" /></h1>
        </div>
    </div>
    
    <div class="nav">
        <div class="container nav-container">
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
            <div class="nav-links">
                <a href="index.html" class="active">首页</a>
                <a href="products-app.html">游戏中心</a>
                <!-- <a href="products-wx.html">小程序开发</a>
                <a href="products-soft.html">管理软件开发</a> -->
                <a href="articles.html">防沉迷公告</a>
                <a href="about.html">关于我们</a>
                <!-- <a href="contact.html">联系我们</a> -->
            </div>
        </div>
    </div>
        
    <div class="hero">
        <div class="container">
            <h2>打造精品游戏，创造无限可能</h2>
            <p>专业的游戏开发团队，为您提供从创意到上线的一站式游戏开发服务</p>
            <div class="hero-buttons">
                <a href="about.html" class="hero-button primary-button">关于我们</a>
                <a href="products-app.html" class="hero-button secondary-button">查看游戏</a>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <div class="features">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <h3>游戏开发</h3>
                    <p>专业的游戏开发团队，精通多种游戏引擎和平台，为您打造高品质的游戏产品</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>多平台发布</h3>
                    <p>支持移动端、PC端、网页端多平台游戏开发，让您的游戏覆盖更广泛的用户群体</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>运营支持</h3>
                    <p>提供游戏上线后的运营维护、数据分析、版本更新等全方位支持服务</p>
                </div>
            </div>
        </div>

    <div class="advantages">
        <div class="container">
            <h2>为什么选择我们的游戏开发服务？</h2>
            <div class="advantages-grid">
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3>专业游戏团队</h3>
                    <p>拥有多年游戏开发经验的专业团队，熟悉各种游戏类型和技术框架，确保项目高质量完成。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>创意设计理念</h3>
                    <p>从游戏策划到美术设计，每个环节都注重创新与品质，打造独具特色的游戏体验。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3>技术实力雄厚</h3>
                    <p>掌握Unity、Cocos2d等主流游戏引擎，支持2D/3D游戏开发，技术覆盖全平台。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>数据驱动运营</h3>
                    <p>提供专业的游戏数据分析和运营策略，帮助提升游戏留存率和收益表现。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3>丰富发布经验</h3>
                    <p>熟悉各大应用商店和游戏平台的发布流程，帮助游戏快速上线并获得更好的推广效果。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>防沉迷保障</h3>
                    <p>严格遵循国家相关法规，完善的防沉迷系统和实名认证机制，营造健康游戏环境。</p>
                </div>
            </div>
        </div>

        <!--
        <div class="stats">
            <div class="container">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">200+</div>
                        <div class="stat-label">服务客户</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">500+</div>
                        <div class="stat-label">成功案例</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">技术专家</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">98%</div>
                        <div class="stat-label">客户满意度</div>
                    </div>
                </div>
            </div>
        </div>
        -->
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>

    <script src="js/login.js"></script>
</body>
</html> 