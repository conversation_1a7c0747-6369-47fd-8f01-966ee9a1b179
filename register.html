<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 复用样式 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        /* 头部和导航 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px 0;
            color: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        /* 主要内容区域 */
        .main-content {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 120px);
            padding: 60px 0;
        }

        /* 注册表单样式 */
        .register-container {
            max-width: 450px;
            width: 100%;
            margin: 0 auto;
            padding: 50px 40px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .register-title {
            text-align: center;
            margin-bottom: 40px;
            color: #2c3e50;
            font-size: 2.2em;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            color: #2c3e50;
            font-weight: 500;
            font-size: 1.05em;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 15px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }
        
        /* 优化滑动验证码样式 */
        .slider-container {
            position: relative;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            height: 50px;
            line-height: 50px;
            text-align: center;
            border-radius: 15px;
            margin-bottom: 25px;
            user-select: none;
            touch-action: none;
            overflow: hidden;
            border: 2px solid rgba(102, 126, 234, 0.2);
        }

        .slider-bg {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 13px;
            width: 0;
            transition: width 0.2s ease;
        }

        .slider-button {
            position: absolute;
            left: 2px;
            top: 50%;
            transform: translateY(-50%);
            width: 46px;
            height: 46px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 13px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            z-index: 10;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .slider-button:hover {
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transform: translateY(-50%) scale(1.05);
        }

        .slider-text {
            position: relative;
            z-index: 1;
            color: #5a6c7d;
            transition: color 0.3s;
            font-weight: 500;
        }

        .register-button {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .register-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .register-links {
            margin-top: 30px;
            text-align: center;
        }

        .register-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .register-links a:hover {
            color: #764ba2;
            text-decoration: underline;
        }

        /* 添加禁用按钮样式 */
        .register-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 添加输入框错误状态样式 */
        .form-group input.error {
            border-color: #ff6b6b;
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
        }

        /* 导航样式 */
        .nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .nav-links a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 25px;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        /* 登录注册按钮样式 */
        .nav-auth {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-auth a {
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-auth .login-btn {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .nav-auth .login-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }

        .nav-auth .register-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-auth .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* 发布按钮样式 */
        .publish-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
            color: white !important;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .publish-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .logout-btn {
            color: #2c3e50 !important;
            background: rgba(44, 62, 80, 0.1);
            border: 1px solid rgba(44, 62, 80, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .logout-btn:hover {
            background: rgba(44, 62, 80, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(44, 62, 80, 0.2);
        }

        .user-btns {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-btns a {
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 页脚样式 */
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px 0 20px;
            margin-top: 0;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            /*margin-bottom: 50px;*/
            position: relative;
            z-index: 1;
        }

        .footer-section h4 {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: white;
            position: relative;
            padding-bottom: 8px;
            font-weight: 600;
        }

        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 30px;
            height: 2px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 8px;
        }

        .footer-section ul li a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.95em;
            display: inline-block;
        }

        .footer-section ul li a:hover {
            color: #667eea;
            transform: translateX(5px);
        }

        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .contact-info:hover {
            color: white;
            transform: translateX(5px);
        }

        .contact-info i {
            margin-right: 10px;
            color: #667eea;
            font-size: 1em;
            width: 16px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: rgba(255,255,255,0.6);
            position: relative;
            z-index: 1;
        }

        .footer-bottom p {
            margin-bottom: 5px;
            font-size: 0.85em;
            line-height: 1.4;
        }

        .footer-bottom a {
            color: rgba(255,255,255,0.8);
            transition: color 0.3s ease;
        }

        .footer-bottom a:hover {
            color: #667eea;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 20px;
            }

            .register-container {
                padding: 40px 25px;
            }

            .register-title {
                font-size: 1.8em;
            }
        }

        @media (max-width: 480px) {
            .register-title {
                font-size: 1.6em;
            }

            .register-container {
                padding: 30px 20px;
            }
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><img src="img/logo.png" height="50" /></h1>
        </div>
    </div>

    <div class="nav">
        <div class="container nav-container">
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns" style="display:none">
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html">游戏中心</a>
                <!-- <a href="products-wx.html">小程序开发</a>
                <a href="products-soft.html">管理软件开发</a> -->
                <a href="articles.html">防沉迷公告</a>
                <a href="about.html">关于我们</a>
                <!-- <a href="contact.html">联系我们</a> -->
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="register-container">
            <h2 class="register-title">用户注册</h2>
            <form id="registerForm">
                <div class="form-group">
                    <label for="phone">手机号</label>
                    <input
                        type="tel"
                        id="phone"
                        name="phone"
                        required
                        pattern="^1[3-9]\d{9}$"
                        placeholder="请输入手机号"
                        oninvalid="this.setCustomValidity('请输入正确的11位手机号码')"
                        oninput="this.setCustomValidity('')">
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        required
                        minlength="6"
                        placeholder="请输入密码"
                        oninvalid="this.setCustomValidity('密码长度至少为6位')"
                        oninput="this.setCustomValidity(''); validatePassword()">
                </div>
                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <input
                        type="password"
                        id="confirmPassword"
                        name="confirmPassword"
                        required
                        placeholder="请再次输入密码"
                        oninvalid="this.setCustomValidity('请再次输入密码')"
                        oninput="this.setCustomValidity(''); validatePassword()">
                    <span id="passwordError" style="color: #ff6b6b; font-size: 12px; display: none;">两次输入的密码不一致</span>
                </div>
                <div class="slider-container">
                    <div class="slider-bg"></div>
                    <div class="slider-button">
                        <i class="fas fa-arrows-alt-h"></i>
                    </div>
                    <div class="slider-text">请向右滑动验证</div>
                </div>
                <button type="submit" class="register-button">注册</button>
                <div class="register-links">
                    <span>已有账号？</span>
                    <a href="login.html">立即登录</a>
                </div>
            </form>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>

    <script>
        // 优化滑动验证码实现
        const slider = document.querySelector('.slider-button');
        const bg = document.querySelector('.slider-bg');
        const text = document.querySelector('.slider-text');
        const container = document.querySelector('.slider-container');
        let isMouseDown = false;
        let startX;
        let sliderLeft;
        let isVerified = false;

        function handleStart(event) {
            if (isVerified) return;
            const e = event.type.startsWith('mouse') ? event : event.touches[0];
            isMouseDown = true;
            startX = e.clientX;
            sliderLeft = slider.offsetLeft;
            
            // 添加过渡类
            slider.style.transition = 'none';
            bg.style.transition = 'none';
        }

        function handleMove(event) {
            if (!isMouseDown || isVerified) return;
            event.preventDefault();
            const e = event.type.startsWith('mouse') ? event : event.touches[0];
            const walk = e.clientX - startX;
            let newLeft = sliderLeft + walk;
            
            // 限制滑块移动范围
            const maxLeft = container.clientWidth - slider.clientWidth;
            newLeft = Math.max(0, Math.min(maxLeft, newLeft));
            
            // 更新滑块和背景位置
            requestAnimationFrame(() => {
                slider.style.left = `${newLeft}px`;
                bg.style.width = `${newLeft + slider.clientWidth}px`;
            });
            
            // 验证是否滑到终点
            if (newLeft >= maxLeft - 2) {
                verifySuccess();
            }
        }

        function handleEnd() {
            if (!isVerified && isMouseDown) {
                // 恢复过渡效果
                slider.style.transition = 'left 0.2s ease';
                bg.style.transition = 'width 0.2s ease';
                resetSlider();
            }
            isMouseDown = false;
        }

        function verifySuccess() {
            isVerified = true;
            text.textContent = '验证通过';
            text.style.color = '#52c41a';
            slider.style.backgroundColor = '#52c41a';
            slider.style.transition = 'all 0.3s ease';
            bg.style.transition = 'all 0.3s ease';
            
            const maxLeft = container.clientWidth - slider.clientWidth;
            slider.style.left = `${maxLeft}px`;
            bg.style.width = '100%';
            
            // 移除事件监听
            removeEventListeners();
        }

        function resetSlider() {
            requestAnimationFrame(() => {
                slider.style.left = '0px';
                bg.style.width = '0px';
            });
        }

        function removeEventListeners() {
            slider.removeEventListener('mousedown', handleStart);
            document.removeEventListener('mousemove', handleMove);
            slider.removeEventListener('touchstart', handleStart);
            document.removeEventListener('touchmove', handleMove);
        }

        // 添加事件监听
        slider.addEventListener('mousedown', handleStart);
        document.addEventListener('mousemove', handleMove, { passive: false });
        document.addEventListener('mouseup', handleEnd);
        
        slider.addEventListener('touchstart', handleStart, { passive: false });
        document.addEventListener('touchmove', handleMove, { passive: false });
        document.addEventListener('touchend', handleEnd);
        document.addEventListener('touchcancel', handleEnd);

        // 添加密码验证函数
        function validatePassword() {
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirmPassword');
            const passwordError = document.getElementById('passwordError');
            const registerButton = document.querySelector('.register-button');

            if (confirmPassword.value && password.value !== confirmPassword.value) {
                passwordError.style.display = 'block';
                confirmPassword.style.borderColor = '#ff4d4f';
                registerButton.disabled = true;
                registerButton.style.backgroundColor = '#ccc';
                return false;
            } else {
                passwordError.style.display = 'none';
                confirmPassword.style.borderColor = '#ddd';
                registerButton.disabled = false;
                registerButton.style.backgroundColor = '#2196F3';
                return true;
            }
        }

        // 修改表单提交验证
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!validatePassword()) {
                return;
            }
            
            if (!isVerified) {
                alert('请完成滑动验证');
                return;
            }

            var formData = new FormData(this);
            var body = new URLSearchParams(formData).toString();
            fetch('index.php?action=register',{
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: body
            }).then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP 错误! 状态码: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('获取的数据:', data);
                    if (data.success) {
                        localStorage.setItem('isLoggedIn', 'true');
                        window.location.href = 'index.html';
                    } else {
                        alert(data.message)
                    }
                })
                .catch(error => {
                    console.error('获取数据时出错:', error);
                });
        });
    </script>
</body>
</html> 