<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏详情</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 复用样式 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        /* 头部和导航 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px 0;
            color: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .nav-links a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 25px;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        /* 登录注册按钮样式 */
        .nav-auth {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-auth a {
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-auth .login-btn {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .nav-auth .login-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }

        .nav-auth .register-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-auth .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        /* 主要内容区域 */
        .main-content {
            background: white;
            border-radius: 30px 30px 0 0;
            margin-top: 40px;
            position: relative;
            z-index: 10;
            box-shadow: 0 -10px 40px rgba(0,0,0,0.1);
            padding: 60px 0;
        }

        /* 产品详情样式 */
        .product-detail {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            margin: 0;
        }

        /* 产品图片展示样式 */
        .product-images {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .product-image {
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .product-image:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 60px rgba(102, 126, 234, 0.2);
        }

        .product-image img {
            width: 100%;
            height: auto;
            display: block;
            transition: transform 0.3s ease;
        }

        .product-image:hover img {
            transform: scale(1.02);
        }

        /* 产品信息区域 */
        .product-info h1 {
            font-size: 2.8em;
            color: #2c3e50;
            margin-bottom: 25px;
            font-weight: 700;
            line-height: 1.3;
        }

        .product-info .description {
            color: #5a6c7d;
            font-size: 1.2em;
            line-height: 1.8;
            margin-bottom: 35px;
            padding: 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }

        /* 供应商信息样式 */
        .supplier-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            margin-bottom: 35px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 18px;
            color: #5a6c7d;
            font-size: 1.05em;
            transition: all 0.3s ease;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-item:hover {
            color: #2c3e50;
            transform: translateX(5px);
        }

        .info-item i {
            color: #667eea;
            margin-right: 15px;
            width: 25px;
            text-align: center;
            font-size: 1.2em;
        }

        .info-item span:first-of-type {
            margin-right: 8px;
            color: #2c3e50;
            font-weight: 600;
            min-width: 80px;
        }

        /* 功能特性区域 */
        .features-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 35px;
            border-radius: 20px;
            margin-bottom: 35px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .features-section h2 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 2em;
            font-weight: 600;
            position: relative;
            padding-bottom: 15px;
        }

        .features-section h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        .features-list {
            list-style: none;
        }

        .features-list li {
            margin-bottom: 18px;
            padding-left: 35px;
            position: relative;
            font-size: 1.1em;
            color: #5a6c7d;
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .features-list li:hover {
            color: #2c3e50;
            transform: translateX(5px);
        }

        .features-list li:before {
            content: '✓';
            color: #667eea;
            position: absolute;
            left: 0;
            font-weight: bold;
            font-size: 1.3em;
        }

        /* 联系按钮 */
        .contact-button {
            display: inline-block;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 18px 45px;
            border-radius: 50px;
            text-decoration: none;
            font-size: 1.2em;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            margin-top: 25px;
            box-shadow: 0 8px 30px rgba(255, 107, 107, 0.4);
            position: relative;
            overflow: hidden;
        }

        .contact-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
        }
        /* 页脚样式 */
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px 0 20px;
            margin-top: 0;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            /*margin-bottom: 50px;*/
            position: relative;
            z-index: 1;
        }

        .footer-section h4 {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: white;
            position: relative;
            padding-bottom: 8px;
            font-weight: 600;
        }

        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 30px;
            height: 2px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 8px;
        }

        .footer-section ul li a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.95em;
            display: inline-block;
        }

        .footer-section ul li a:hover {
            color: #667eea;
            transform: translateX(5px);
        }

        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .contact-info:hover {
            color: white;
            transform: translateX(5px);
        }

        .contact-info i {
            margin-right: 10px;
            color: #667eea;
            font-size: 1em;
            width: 16px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: rgba(255,255,255,0.6);
            position: relative;
            z-index: 1;
        }

        .footer-bottom p {
            margin-bottom: 5px;
            font-size: 0.85em;
            line-height: 1.4;
        }

        .footer-bottom a {
            color: rgba(255,255,255,0.8);
            transition: color 0.3s ease;
        }

        .footer-bottom a:hover {
            color: #667eea;
        }

        /* 发布按钮样式 */
        .publish-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
            color: white !important;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .publish-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .logout-btn {
            color: #2c3e50 !important;
            background: rgba(44, 62, 80, 0.1);
            border: 1px solid rgba(44, 62, 80, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .logout-btn:hover {
            background: rgba(44, 62, 80, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(44, 62, 80, 0.2);
        }

        /* 游戏元信息样式 */
        .game-meta-info {
            margin: 20px 0;
        }

        .meta-tags {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .category-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .age-tag {
            background: #ff9500;
            color: white;
            padding: 8px 16px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .user-btns {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-btns a {
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 20px;
            }

            .product-detail {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .product-info h1 {
                font-size: 2.2em;
            }

            .nav-links {
                gap: 20px;
            }

            .nav-auth {
                gap: 15px;
            }

            .nav-auth a,
            .user-btns a {
                padding: 10px 20px;
                font-size: 0.9em;
            }

            .features-section,
            .supplier-info {
                padding: 25px 20px;
            }
        }

        @media (max-width: 480px) {
            .product-info h1 {
                font-size: 1.8em;
            }

            .nav-container {
                flex-direction: column;
                height: auto;
                gap: 20px;
            }

            .product-detail {
                gap: 30px;
            }

            .contact-button {
                padding: 15px 35px;
                font-size: 1.1em;
            }
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><img src="img/logo.png" height="50" /></h1>
        </div>
    </div>

    <div class="nav">
        <div class="container nav-container">
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html" class="active">游戏中心</a>
                <!-- <a href="products-wx.html">小程序开发</a>
                <a href="products-soft.html">管理软件开发</a> -->
                <a href="articles.html">防沉迷公告</a>
                <a href="about.html">关于我们</a>
                <!-- <a href="contact.html">联系我们</a> -->
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <div class="product-detail">
                <div class="product-images" id="productImages">
                    <!-- 图片将通过 JavaScript 动态添加 -->
                </div>
                <div class="product-info">
                    <h1 id="productTitle"></h1>
                    
                    <!-- 游戏分类和适龄信息 -->
                    <div class="game-meta-info" id="gameMetaInfo" style="display: none;">
                        <div class="meta-tags">
                            <span class="category-tag" id="categoryTag"></span>
                            <span class="age-tag" id="ageTag"></span>
                        </div>
                    </div>
                    
                    <p class="description" id="productDesc"></p>

                    <!-- 添加供应商信息区域 -->
                    <!-- <div class="supplier-info">
                        <div class="info-item">
                            <i class="fas fa-user"></i>
                            <span>发布自：</span>
                            <span id="publisher"></span>
                        </div> 
                         <div class="info-item">
                            <i class="fas fa-building"></i>
                            <span>供应商：</span>
                            <span id="supplier"></span>
                        </div> 
                         <div class="info-item">
                            <i class="fas fa-phone"></i>
                            <span>联系电话：</span>
                            <span id="contactPhone"></span>
                        </div>
                    </div>

                    <div class="features-section">
                        <h2>主要功能</h2>
                        <ul class="features-list" id="featuresList"></ul>
                    </div> -->
                    <a href="javascript:void(0)" id="downUrl" class="contact-button" >立即下载</a>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>

    <script>
        // 产品数据
        const products = [];
        // 游戏数据 - 与游戏中心页面保持一致
        
        const gamesList = [
            {
                id: 'g1',
                name: '诸侯征战',
                image: 'img/app/1/icon.png',
                images: [
                    'img/app/1/d1.jpg',
                    'img/app/1/d2.jpg',
                    'img/app/1/d3.jpg',
                    'img/app/1/d4.jpg',
                ],
                category: '战略',
                ageRating: '全年龄段',
                description: '一款复古纯粹的模拟经营三国手游。多人合作，疯狂国战，只有最好的策略才能赢得国战。大王快点来吹起集结号，奏响三国战歌！与兄弟们打下第一战国吧！ 在这里，您可以与朋友团结合作，统一指挥，千人国战；在这里，您需要警惕虎视眈眈的邻居，保卫自己的资源；在这里，您可以低调经营自己的城池，享受成长的满足；在这里，也可以纵横捭阖，领略与人争斗的其乐无穷。',
                features: [],
                publisher: '',
                supplier: '',
                contactPhone: '',
                downloadUrl: 'https://cdn.910app.com/android/downloads/zhzz-7750001-20250107164317.apk'
            },
            {
                id: 'g2',
                name: '朕的江山2',
                image: 'img/app/2/icon.png',
                images: [
                    'img/app/2/d1.jpg',
                    'img/app/2/d2.jpg',
                    'img/app/2/d3.jpg',
                    'img/app/2/d4.jpg',
                ],
                category: '战略',
                ageRating: '全年龄段',
                description: '《朕的江山2》是一款经典三国战役SLG手游。连城式大地图还原真实的三国战场，全服玩家同时征伐战场冲锋陷阵，四大兵种三类将领互相制衡，任君招募的上白名历史名将，高能的策略排兵布阵方能赢得国战，一统江山，战鼓齐鸣一起书写新的三国激战历史！',
                features: [],
                publisher: '',
                supplier: '',
                contactPhone: '',
                downloadUrl: 'https://cdn.910app.com/android/downloads/zdjs2-60040040-20250325151214.apk'
            },
            {
                id: 'g3',
                name: '不朽大陆 ',
                image: 'img/app/3/icon.png',
                images: [
                    'img/app/3/d1.jpg',
                    'img/app/3/d2.jpg',
                    'img/app/3/d3.jpg',
                    'img/app/3/d4.jpg',
                ],
                category: '卡牌',
                ageRating: '全年龄段',
                description: '不朽大陆官方版是一款美漫式放置卡牌游戏，在故事背景中一场突如其来的未知病毒，爆发了外星人入侵的事实，为了阻止这场浩劫，你被选中为指挥官，现在你需要带领其他组织成员，寻找病毒源头并解放城市。',
                features: [],
                publisher: '',
                supplier: '',
                contactPhone: '',
                downloadUrl: 'https://cdn.910app.com/android/downloads/bxdl-63560001-20250427120034.apk'
            },
            {
                id: 'g4',
                name: '攻城掠地',
                image: 'img/app/4/icon.png',
                images: [
                    'img/app/4/d1.jpg',
                    'img/app/4/d2.jpg',
                    'img/app/4/d3.jpg',
                    'img/app/4/d4.jpg',
                ],
                category: '策略',
                ageRating: '全年龄段',
                description: '《攻城掠地》是一款强调“国战”的战争策略游戏，本作由《傲视天地》原班人马创新打造，秉承经典，推陈出新。实景还原三国战图，包含多达300个关隘城池，开创全景即时国战!文臣武将齐数登场，打破“重武轻文”的游戏桎梏，内政事务、军师上阵、计策绝技演绎文官真本色!其次，递进掩杀式即时战斗模式、地形系统的引入，以及四大资源、战术、兵器、科技、皇城官职战等丰富的特色玩法，让你充分自由的享受鼎足争雄的热血，圆满开疆扩土、统一天下的宏梦!',
                features: [],
                publisher: '',
                supplier: '',
                contactPhone: '',
                downloadUrl: 'https://cdn.910app.com/android/downloads/gcld.apk'
            },
            {
                id: 'g5',
                name: '割据天下',
                image: 'img/app/5/icon.png',
                images: [
                    'img/app/5/d1.jpg',
                    'img/app/5/d2.jpg',
                    'img/app/5/d3.jpg',
                    'img/app/5/d4.jpg',
                ],
                category: '战略',
                ageRating: '全年龄段',
                description: ' 《割据天下》是一款文明题材策略战争手游。游戏内设计了宏伟的战争场面，多元文化和不同种族的热血碰撞，你将亲自上阵，巧用天时地利人和，创立基业，组建联盟、自由扩张，智取天下，谱写属于你的传奇时代。',
                features: [],
                publisher: '',
                supplier: '',
                contactPhone: '',
                downloadUrl: 'https://cdn.910app.com/android/downloads/gjtx-67240001-20240510104416.apk'
            },
            {
                id: 'g6',
                name: '帝国霸权',
                image: 'img/app/6/icon.png',
                images: [
                    'img/app/6/d1.jpg',
                    'img/app/6/d2.jpg',
                    'img/app/6/d3.jpg',
                    'img/app/6/d4.jpg',
                ],
                category: '战略',
                ageRating: '16+',
                description: '帝国霸权是一款以中世纪时期罗马帝国为原型打造slg手游，玩家可以培养招募兵马训练将领，玩家将在游戏找那个建造自己的专属帝国，领袖意识，拥有多种不同种族的士兵，统帅一支强大的军队',
                features: [],
                publisher: '',
                supplier: '',
                contactPhone: '',
                downloadUrl: 'https://cdn.910app.com/android/downloads/dgbq-24310040-20220629114329.apk'
            },
        ];

        // 合并所有产品数据（包括小程序和游戏）
        const allProducts = [...products, ...gamesList];

        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const productId = urlParams.get('id');

        // 声明全局变量存储电话号码
        let fullPhone = '';

        // 更新页面内容
        const product = allProducts.find(p => p.id === productId);
        if (product) {
            document.title = `${product.name} - 详情`;
            
            // 添加所有图片
            const productImages = document.getElementById('productImages');
            product.images.forEach(image => {
                const imageDiv = document.createElement('div');
                imageDiv.className = 'product-image';
                imageDiv.innerHTML = `<img src="${image}" alt="${product.name}">`;
                productImages.appendChild(imageDiv);
            });

            // 更新产品信息
            document.getElementById('productTitle').textContent = product.name;
            
            // 如果是游戏（ID以'g'开头），显示游戏特有信息
            if (product.id.startsWith('g')) {
                const gameMetaInfo = document.getElementById('gameMetaInfo');
                const categoryTag = document.getElementById('categoryTag');
                const ageTag = document.getElementById('ageTag');
                
                gameMetaInfo.style.display = 'block';
                categoryTag.textContent = product.category;
                ageTag.textContent = product.ageRating;
            }
            
            document.getElementById('productDesc').textContent = product.description;
            
            // 存储完整电话号码
            fullPhone = product.phone;
            
            // 更新供应商信息
            // document.getElementById('publisher').textContent = product.publisher;
            // document.getElementById('supplier').textContent = product.supplier;
            // document.getElementById('contactPhone').textContent = product.contactPhone;
            document.getElementById('downUrl').href = product.downloadUrl;
            
            const featuresList = document.getElementById('featuresList');
            product.features.forEach(feature => {
                const li = document.createElement('li');
                li.textContent = feature;
                featuresList.appendChild(li);
            });
        }

        // 添加显示完整电话号码的函数
        function showFullPhone() {
            document.getElementById('contactPhone').textContent = fullPhone;
        }
    </script>
    <script src="js/login.js"></script>
</body>
</html> 